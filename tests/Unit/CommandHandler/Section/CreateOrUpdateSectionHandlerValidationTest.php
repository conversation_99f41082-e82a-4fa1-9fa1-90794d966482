<?php

declare(strict_types=1);

namespace App\Tests\Unit\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CreateOrUpdateSectionHandlerValidationTest extends TestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
    }

    public function testCommandValidationWithEmptyName(): void
    {
        $command = new CreateOrUpdateSection(
            name: '',  // Invalid: empty name
            sectionType: SectionType::GeneralHealth,
            published: true
        );

        $violation = new ConstraintViolation(
            'This value should not be blank.',
            'This value should not be blank.',
            [],
            '',
            'name',
            ''
        );
        $violations = new ConstraintViolationList([$violation]);

        // Test that we can create violations and check them
        $violationCount = count($violations);
        $this->assertEquals(1, $violationCount);
        $this->assertEquals('This value should not be blank.', $violations[0]->getMessage());

        // Test command properties
        $this->assertEquals('', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
    }

    public function testCommandValidationWithValidData(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: true
        );

        // Test command properties
        $this->assertEquals('Test Section', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
        $this->assertTrue($command->isPublished());

        // Test that empty violations list works
        $emptyViolations = new ConstraintViolationList([]);
        $this->assertEquals(0, count($emptyViolations));
    }

    public function testCommandWithProductSections(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::MedicalCondition,
            published: true,
            medicalConditionSections: [
                ['medicalConditionId' => 'MC123', 'name' => 'Test Condition']
            ],
            productSections: [
                ['productId' => 'P456', 'name' => 'Test Product']
            ]
        );

        $this->assertCount(1, $command->getMedicalConditionSections());
        $this->assertCount(1, $command->getProductSections());
        $this->assertEquals('MC123', $command->getMedicalConditionSections()[0]['medicalConditionId']);
        $this->assertEquals('P456', $command->getProductSections()[0]['productId']);
    }

    public function testCommandWithQuestionSections(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::Other,
            published: true,
            questionSections: [
                ['question' => 1, 'sort' => 0],
                ['question' => 2, 'sort' => 1]
            ]
        );

        $this->assertCount(2, $command->getQuestionSections());
        $this->assertEquals(1, $command->getQuestionSections()[0]['question']);
        $this->assertEquals(0, $command->getQuestionSections()[0]['sort']);
    }

    public function testCommandWithDeprecatedStatusField(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: false,
            status: true // Deprecated field
        );

        $this->assertFalse($command->isPublished());
        $this->assertTrue($command->getStatus());
    }

    public function testCommandArrayValidation(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: true,
            medicalConditionSections: [],
            productSections: [],
            questionSections: []
        );

        $this->assertIsArray($command->getMedicalConditionSections());
        $this->assertIsArray($command->getProductSections());
        $this->assertIsArray($command->getQuestionSections());
        $this->assertEmpty($command->getMedicalConditionSections());
        $this->assertEmpty($command->getProductSections());
        $this->assertEmpty($command->getQuestionSections());
    }
}
