<?php

declare(strict_types=1);

namespace App\Tests\Unit\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;

class CreateOrUpdateSectionHandlerTest extends TestCase
{
    public function testCommandHandlerIntegrationWithoutDatabase(): void
    {
        // This test verifies that our Command can be created and contains the expected data
        // The actual handler would be tested in integration tests with a real database

        $command = new CreateOrUpdateSection(
            name: 'Test Section',
            sectionType: SectionType::GeneralHealth,
            published: true,
            status: true,
            medicalConditionSections: [
                ['medicalConditionId' => '123', 'name' => 'Test Condition']
            ],
            productSections: [
                ['productId' => '456', 'name' => 'Test Product']
            ],
            questionSections: [
                ['question' => 1, 'sort' => 0]
            ]
        );

        // Verify command properties
        $this->assertEquals('Test Section', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
        $this->assertTrue($command->isPublished());
        $this->assertTrue($command->getStatus());
        $this->assertCount(1, $command->getMedicalConditionSections());
        $this->assertCount(1, $command->getProductSections());
        $this->assertCount(1, $command->getQuestionSections());

        // Verify medical condition section structure
        $medicalConditionSection = $command->getMedicalConditionSections()[0];
        $this->assertEquals('123', $medicalConditionSection['medicalConditionId']);
        $this->assertEquals('Test Condition', $medicalConditionSection['name']);

        // Verify product section structure
        $productSection = $command->getProductSections()[0];
        $this->assertEquals('456', $productSection['productId']);
        $this->assertEquals('Test Product', $productSection['name']);

        // Verify question section structure
        $questionSection = $command->getQuestionSections()[0];
        $this->assertEquals(1, $questionSection['question']);
        $this->assertEquals(0, $questionSection['sort']);
    }

    public function testCommandWithMinimalData(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Minimal Section',
            sectionType: SectionType::Other,
            published: false
        );

        $this->assertEquals('Minimal Section', $command->getName());
        $this->assertEquals(SectionType::Other, $command->getSectionType());
        $this->assertFalse($command->isPublished());
        $this->assertNull($command->getStatus());
        $this->assertEmpty($command->getMedicalConditionSections());
        $this->assertEmpty($command->getProductSections());
        $this->assertEmpty($command->getQuestionSections());
        $this->assertNull($command->isDeleted());
    }
}
